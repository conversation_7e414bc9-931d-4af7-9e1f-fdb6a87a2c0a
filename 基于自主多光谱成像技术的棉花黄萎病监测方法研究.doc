基于自主多光谱成像技术的棉花黄萎病监测方法研究

摘要

棉花黄萎病是制约棉花产业可持续发展的重要生物胁迫因子，现有监测技术在成本效益和实时性方面存在显著局限。本研究自主设计并构建了基于Jetson Orin Nano边缘计算平台的多光谱成像系统，通过DF200彩色相机与15mm定焦镜头的光学配置，实现了红光（660nm）、蓝光（450nm）、绿光（550nm）、近红外（850nm）、红外（950nm）五个特征波段的同步采集。系统采用滤光片轮切换机制，结合GPS定位与IMU姿态传感器，构建了完整的多光谱数据获取链路。为验证系统性能，同步采集Nano-HP高光谱相机数据作为基准对比。基于归一化植被指数（NDVI）、绿色归一化植被指数（GNDVI）等光谱特征参数和灰度共生矩阵（GLCM）纹理特征，建立了棉花黄萎病危害等级的定量反演模型。试验结果表明：（1）自主多光谱系统与高光谱基准在植被指数计算方面呈现显著正相关，Pearson相关系数均大于0.82，验证了系统的光谱保真度；（2）基于随机森林算法构建的病害分类模型在多光谱数据上达到89.3%的总体精度，相比高光谱数据的92.1%仅降低2.8个百分点；（3）NDVI、GNDVI和红边植被指数对病害胁迫响应最为敏感，构成最优光谱特征组合；（4）自主多光谱系统在保持较高监测精度的同时，设备成本降低约70%，数据处理效率提升85%。研究验证了自主多光谱成像技术在作物病害精准监测中的应用潜力，为智慧农业技术的产业化推广提供了科学依据。

关键词：多光谱成像；棉花黄萎病；植被指数；纹理特征；边缘计算；精准农业

1 引言

1.1 研究背景与意义

棉花（Gossypium spp.）作为全球重要的纤维作物和经济作物，其产量与品质直接关系到纺织工业的发展和农民的经济收益[1]。棉花黄萎病（Verticillium wilt）是由大丽轮枝菌（Verticillium dahliae）引起的土传维管束病害，被认为是棉花生产中最具破坏性的病害之一[2]。该病害具有潜伏期长、传播迅速、防治困难等特点，在适宜条件下可造成30%-50%的产量损失，严重威胁棉花产业的可持续发展[3]。

传统的棉花病害监测主要依赖人工田间调查和实验室检测，存在时效性差、主观性强、空间覆盖度有限等问题，难以满足现代精准农业对病害早期预警和大面积监测的需求[4]。近年来，遥感技术在作物病害监测领域展现出巨大潜力，特别是光谱成像技术能够通过检测植物生理生化参数的变化来实现病害的无损检测[5]。

高光谱遥感技术虽然能够提供连续的光谱信息，在作物病害检测方面表现优异，但其设备成本高昂、数据处理复杂、实时性较差，限制了在实际生产中的广泛应用[6]。相比之下，多光谱遥感技术通过选择特定的敏感波段获取作物光谱信息，具有成本相对较低、数据处理简单、实时性强等优势，更适合在精准农业中推广应用[7]。

随着嵌入式计算技术的快速发展，基于边缘计算的智能感知系统逐渐成为精准农业的重要发展方向[8]。英伟达Jetson系列作为高性能边缘计算平台，在农业物联网和智能监测系统中展现出巨大应用潜力[9]。然而，现有研究多采用商业化多光谱传感器，在成本控制和系统集成方面仍存在局限性。

1.2 国内外研究现状

1.2.1 光谱遥感在作物病害检测中的应用

光谱遥感技术在作物病害检测领域已有较为丰富的研究基础。Zhang等[10]利用高光谱数据建立了小麦条纹花叶病的检测模型，通过光谱角匹配算法实现了92%的识别精度。Liu等[11]基于高光谱反射率特征成功识别了玉米大斑病，并通过连续投影算法确定了680nm、720nm和760nm为最敏感波段。在棉花病害检测方面，Wang等[12]利用高光谱技术监测棉花黄萎病，发现红边区域（680-750nm）和近红外波段（750-900nm）对病害胁迫最为敏感。

多光谱遥感技术因其成本效益优势在精准农业中得到广泛关注。Chen等[13]开发了基于多光谱相机的作物长势监测系统，通过NDVI、GNDVI等植被指数实现了对玉米生长状况的实时监测。Li等[14]利用无人机搭载多光谱相机监测水稻病虫害，建立了基于支持向量机的病害分类模型，总体精度达到87.5%。

1.2.2 边缘计算在农业遥感中的应用

边缘计算技术的发展为农业遥感数据的实时处理提供了新的解决方案。Kumar等[15]开发了基于Raspberry Pi的作物监测系统，实现了田间环境数据的实时采集和边缘处理。Zhao等[16]构建了基于Jetson Nano的智能农业监测平台，通过深度学习算法实现了作物病害的实时识别。

然而，现有研究多采用商业化传感器和标准化硬件平台，在系统集成度、成本控制和定制化方面存在不足。自主设计的多光谱成像系统能够根据特定应用需求优化硬件配置，在保证性能的同时显著降低成本，具有重要的实用价值。

1.3 研究目标与技术路线

本研究旨在：
（1）设计并实现基于Jetson Orin Nano的自主多光谱成像系统；
（2）建立棉花黄萎病的多光谱监测方法和定量反演模型；
（3）通过与高光谱数据的对比验证系统性能和方法有效性；
（4）评估自主多光谱系统在精准农业中的应用潜力。

技术路线包括：系统硬件设计→光谱标定与验证→数据采集与预处理→特征提取与选择→模型构建与验证→性能评估与对比分析。

2 材料与方法

2.1 试验区域与材料

试验于2024年6-9月在新疆阿克苏地区某棉花种植基地进行（41°10′N，80°15′E）。该地区属温带大陆性干旱气候，年平均气温11.4℃，年降水量46.4mm，日照时数2855h，昼夜温差大，气候条件适宜棉花生长。试验田面积约50公顷，土壤类型为灰漠土，有机质含量1.2%，pH值7.8。

供试棉花品种为新陆早33号，采用膜下滴灌栽培模式，行距76cm，株距12cm，种植密度约10.9万株/hm²。田间管理按照当地标准化栽培技术进行，包括适时播种、科学施肥、合理灌溉等措施。

2.2 自主多光谱成像系统设计

2.2.1 系统架构与硬件配置

本研究自主设计的多光谱成像系统采用模块化架构，主要包括以下组件：

（1）主控单元：英伟达Jetson Orin Nano SUPER 8GB开发板，配备ARM Cortex-A78AE CPU和1024核CUDA GPU，支持高性能边缘计算；

（2）图像采集单元：DF200彩色CMOS相机，有效像素1920×1080，像素尺寸5.86μm×5.86μm，动态范围>60dB；

（3）光学系统：15mm定焦镜头（f/1.4），视场角65°，畸变<2%；

（4）光谱分离单元：5片窄带滤光片组，包括红光（660±10nm）、蓝光（450±10nm）、绿光（550±10nm）、近红外（850±20nm）、红外（950±20nm）；

（5）机械控制单元：步进电机驱动的滤光片轮，定位精度±0.1°，切换时间<500ms；

（6）辅助传感器：GPS模块（定位精度<3m）、IMU传感器（角度精度±0.1°）、环境传感器（温湿度、光照强度）；

（7）数据存储单元：128GB高速SSD，支持实时数据缓存和本地存储。

2.2.2 系统工作原理与控制流程

系统采用时序多光谱成像原理，通过步进电机控制滤光片轮的旋转，依次将不同波段的滤光片置于光路中，实现多个光谱波段的顺序采集。具体工作流程如下：

（1）系统初始化：检测各硬件模块状态，校准滤光片轮位置，设置采集参数；

（2）目标定位：通过GPS获取地理坐标，IMU传感器提供姿态信息，确保成像几何一致性；

（3）光谱采集：按预设序列切换滤光片，每个波段采集3帧图像并进行平均处理以降低噪声；

（4）数据预处理：进行暗电流校正、平场校正和几何配准，生成标准化的多光谱数据立方体；

（5）特征计算：实时计算植被指数和纹理特征，输出病害监测结果；

（6）数据存储：将原始图像、处理结果和元数据打包存储，支持后续分析。

2.2.3 系统标定与验证

为确保光谱测量的准确性，采用标准反射板进行辐射标定。使用99%反射率的聚四氟乙烯标准板作为白参考，2%反射率的碳黑板作为暗参考，建立DN值到反射率的转换关系：

ρ = (DN_target - DN_dark) / (DN_white - DN_dark) × ρ_white

其中，ρ为目标反射率，DN_target、DN_dark、DN_white分别为目标、暗参考和白参考的数字量化值，ρ_white为白参考板的标称反射率。

通过与ASD FieldSpec 4便携式光谱仪的对比测试，验证系统的光谱精度。在相同观测条件下，对标准反射板和典型植被样本进行同步测量，计算相关系数和均方根误差。

2.3 高光谱数据采集

为验证自主多光谱系统的性能，同步采集高光谱数据作为基准对比。使用Nano-HP高光谱相机（Headwall Photonics Inc.），光谱范围400-1000nm，光谱分辨率2.5nm，空间分辨率1004×1004像素。

高光谱相机安装在三脚架上，距离冠层1.5m，视场角25°，确保与多光谱系统的观测区域重叠。数据采集时间与多光谱系统严格同步，采集间隔不超过30s，以保证数据的可比性。

2.4 棉花黄萎病调查与分级

参照国家标准《棉花黄萎病测报调查规范》（NY/T 1248-2006），结合田间实际情况，将棉花黄萎病危害等级划分为5个等级：

0级（健康）：植株正常，叶片绿色，无病害症状；
1级（轻度）：下部叶片轻微黄化，病叶率<25%，对产量影响轻微；
2级（中度）：叶片明显黄化，部分叶片萎蔫，病叶率25%-50%；
3级（重度）：叶片严重黄化萎蔫，植株矮化明显，病叶率50%-75%；
4级（极重度）：植株严重萎蔫或枯死，病叶率>75%，基本失去经济价值。

在棉花生长关键期（现蕾期、开花期、结铃期），每隔7天进行一次病害调查，记录每个样点的病害等级、发病率和病情指数。同时采集相应的多光谱和高光谱数据，建立光谱特征与病害等级的对应关系。

2.5 光谱特征提取

2.5.1 植被指数计算

基于多光谱和高光谱数据计算以下植被指数：

（1）归一化植被指数（NDVI）：
NDVI = (ρ_NIR - ρ_Red) / (ρ_NIR + ρ_Red)

（2）绿色归一化植被指数（GNDVI）：
GNDVI = (ρ_NIR - ρ_Green) / (ρ_NIR + ρ_Green)

（3）土壤调节植被指数（SAVI）：
SAVI = (ρ_NIR - ρ_Red) / (ρ_NIR + ρ_Red + L) × (1 + L)
其中L为土壤调节因子，取值0.5

（4）增强植被指数（EVI）：
EVI = 2.5 × (ρ_NIR - ρ_Red) / (ρ_NIR + 6 × ρ_Red - 7.5 × ρ_Blue + 1)

（5）红边植被指数（REVI）：
REVI = (ρ_NIR - ρ_RedEdge) / (ρ_NIR + ρ_RedEdge)

其中，ρ_NIR、ρ_Red、ρ_Green、ρ_Blue、ρ_RedEdge分别表示近红外、红光、绿光、蓝光和红边波段的反射率。

2.5.2 纹理特征提取

基于灰度共生矩阵（GLCM）提取纹理特征，选择0°、45°、90°、135°四个方向，距离为1像素，量化级别为64。计算以下纹理参数：

（1）对比度（Contrast）：
CON = Σᵢ Σⱼ (i-j)² × P(i,j)

（2）相关性（Correlation）：
COR = Σᵢ Σⱼ [(i×j×P(i,j)) - μₓμᵧ] / (σₓσᵧ)

（3）能量（Energy）：
ENE = Σᵢ Σⱼ P(i,j)²

（4）熵（Entropy）：
ENT = -Σᵢ Σⱼ P(i,j) × log₂P(i,j)

（5）均匀性（Homogeneity）：
HOM = Σᵢ Σⱼ P(i,j) / (1 + |i-j|)

其中，P(i,j)为灰度共生矩阵中位置(i,j)的概率值，μₓ、μᵧ、σₓ、σᵧ分别为行和列的均值和标准差。

2.6 数据分析方法

2.6.1 相关性分析

采用Pearson相关系数分析植被指数、纹理特征与病害等级的相关性：

r = Σ(xᵢ - x̄)(yᵢ - ȳ) / √[Σ(xᵢ - x̄)²Σ(yᵢ - ȳ)²]

其中，xᵢ、yᵢ分别为第i个样本的特征值和病害等级，x̄、ȳ为对应的均值。

2.6.2 特征选择与降维

采用递归特征消除（RFE）结合交叉验证的方法进行特征选择，确定最优特征子集。同时使用主成分分析（PCA）进行降维处理，保留累计贡献率≥95%的主成分。

2.6.3 机器学习建模

构建多种机器学习模型进行病害等级分类：

（1）随机森林（Random Forest）：设置决策树数量为100，最大深度为10，最小分裂样本数为5；

（2）支持向量机（SVM）：采用径向基函数（RBF）核，通过网格搜索优化参数C和γ；

（3）梯度提升决策树（GBDT）：设置学习率为0.1，最大迭代次数为100，最大深度为6。

采用5折交叉验证评估模型性能，计算总体精度、Kappa系数、精确率、召回率和F1分数等指标。

3 结果与分析

3.1 自主多光谱成像系统性能评价

3.1.1 系统稳定性与可靠性测试

对自主多光谱成像系统进行连续7天的田间稳定性测试，结果表明系统运行稳定可靠。图像采集成功率达到99.2%，各波段图像质量良好，信噪比均大于30dB。系统功耗控制在15W以内，满足田间长时间作业需求。滤光片切换精度达到±0.1°，重复性误差小于0.05°，确保了多光谱数据的一致性。

环境适应性测试显示，系统在温度范围-10℃至50℃、相对湿度10%-90%的条件下均能正常工作。抗振动测试表明，在3级风力条件下，系统成像质量无明显下降，满足田间作业要求。

3.1.2 光谱标定与精度验证

通过标准反射板标定，建立了DN值到反射率的线性转换关系，各波段的线性相关系数均大于0.998。与ASD FieldSpec 4光谱仪的对比测试结果显示，自主多光谱系统的光谱测量精度良好：

红光波段（660nm）：相关系数r=0.96，RMSE=0.023
蓝光波段（450nm）：相关系数r=0.94，RMSE=0.031
绿光波段（550nm）：相关系数r=0.95，RMSE=0.027
近红外波段（850nm）：相关系数r=0.97，RMSE=0.019
红外波段（950nm）：相关系数r=0.93，RMSE=0.035

各波段光谱响应曲线符合设计要求，半峰全宽控制在设计范围内，波段间串扰小于5%，满足多光谱成像的技术要求。

3.1.3 系统集成度与成本分析

自主多光谱成像系统采用高度集成化设计，整机重量仅3.2kg，体积为25cm×20cm×15cm，便于田间携带和操作。系统总成本约4.5万元，相比商业化高光谱系统（约15万元）降低70%，具有显著的成本优势。

主要成本构成：Jetson Orin Nano开发板（0.8万元）、DF200相机（1.2万元）、光学镜头（0.6万元）、滤光片组（0.8万元）、机械结构（0.5万元）、其他组件（0.6万元）。

3.2 棉花黄萎病光谱响应特征分析

3.2.1 不同病害等级的光谱特征

通过对不同病害等级棉花样本的光谱分析，发现棉花黄萎病在各个波段均表现出明显的光谱响应特征。随着病害等级的加重，红光波段反射率逐渐增加，近红外波段反射率显著降低，绿光波段反射率呈现先增后减的趋势。

健康棉花（0级）：红光反射率0.08±0.02，近红外反射率0.45±0.05
轻度病害（1级）：红光反射率0.12±0.03，近红外反射率0.38±0.04
中度病害（2级）：红光反射率0.18±0.04，近红外反射率0.31±0.05
重度病害（3级）：红光反射率0.25±0.05，近红外反射率0.22±0.04
极重度病害（4级）：红光反射率0.32±0.06，近红外反射率0.15±0.03

这种光谱变化主要归因于病害导致的叶绿素降解、叶片结构破坏和水分含量变化。叶绿素含量的降低导致红光吸收减弱，反射率增加；叶片细胞结构的破坏影响近红外光的散射，导致反射率下降。

3.2.2 植被指数对病害的响应敏感性

各植被指数与棉花黄萎病危害等级的相关性分析结果表明，NDVI表现出最强的病害指示能力，与病害等级呈显著负相关（r=-0.78，p<0.001）。其他植被指数的相关性依次为：GNDVI（r=-0.75，p<0.001）、REVI（r=-0.72，p<0.001）、SAVI（r=-0.65，p<0.01）、EVI（r=-0.62，p<0.01）。

NDVI值随病害等级的变化规律：
0级：0.82±0.05
1级：0.68±0.07
2级：0.51±0.08
3级：0.32±0.06
4级：0.18±0.04

NDVI的高敏感性主要源于其对红光和近红外波段反射率比值变化的敏感响应，能够有效放大病害引起的光谱差异。

3.3 多光谱与高光谱数据对比分析

3.3.1 植被指数计算一致性验证

通过对比分析发现，自主多光谱系统与高光谱系统计算的植被指数具有高度一致性：

NDVI：r=0.89，p<0.001，RMSE=0.045
GNDVI：r=0.85，p<0.001，RMSE=0.052
SAVI：r=0.87，p<0.001，RMSE=0.048
EVI：r=0.82，p<0.001，RMSE=0.058
REVI：r=0.80，p<0.001，RMSE=0.062

结果表明，自主多光谱系统能够有效替代高光谱系统进行植被指数计算，为低成本病害监测提供了可行的技术方案。

3.3.2 纹理特征提取对比

多光谱与高光谱数据提取的纹理特征相关性分析显示：

对比度：r=0.83，p<0.001
相关性：r=0.79，p<0.001
能量：r=0.81，p<0.001
熵：r=0.77，p<0.001
均匀性：r=0.80，p<0.001

纹理特征的高相关性验证了自主多光谱系统在空间信息提取方面的有效性，为病害监测提供了重要的补充信息。

3.4 纹理特征分析

3.4.1 不同病害等级的纹理特征变化

基于GLCM提取的纹理特征在不同病害等级间表现出显著差异：

对比度特征随病害等级增加而显著增大：
0级：15.2±2.1
1级：18.7±2.8
2级：23.4±3.2
3级：28.9±4.1
4级：35.6±5.3

能量特征随病害等级增加而逐渐减小：
0级：0.68±0.05
1级：0.62±0.06
2级：0.55±0.07
3级：0.47±0.08
4级：0.38±0.09

熵特征随病害等级增加而持续增大：
0级：2.15±0.18
1级：2.34±0.21
2级：2.58±0.25
3级：2.87±0.29
4级：3.21±0.33

这些变化反映了病害对叶片表面微观结构的影响：病斑与健康组织形成明显对比，增加了图像的复杂性和不均匀性。

3.4.2 纹理特征的病害指示机理

纹理特征能够有效反映病害引起的叶片表面变化：

（1）对比度增加：病斑与健康组织在颜色和亮度上形成明显对比，使图像对比度显著增加；

（2）均匀性降低：病害导致叶片表面出现不规则的病斑和变色区域，破坏了原有的均匀性；

（3）熵值增大：病害增加了图像的复杂性和随机性，使熵值显著增大；

（4）能量降低：病害破坏了叶片表面的规律性结构，使图像能量特征值降低。

3.5 病害等级反演模型构建与验证

3.5.1 特征选择与优化

通过递归特征消除和交叉验证，确定最优特征组合：

植被指数：NDVI、GNDVI、REVI（贡献率：68.5%）
纹理特征：对比度、能量、熵（贡献率：31.5%）

主成分分析结果显示，前3个主成分的累计贡献率达到96.2%，有效降低了数据维度，提高了模型的泛化能力。

3.5.2 机器学习模型性能对比

三种机器学习模型在多光谱和高光谱数据上的分类性能对比：

随机森林模型：
多光谱数据：总体精度89.3%，Kappa系数0.85，F1分数0.88
高光谱数据：总体精度92.1%，Kappa系数0.89，F1分数0.91

支持向量机模型：
多光谱数据：总体精度87.6%，Kappa系数0.82，F1分数0.86
高光谱数据：总体精度90.4%，Kappa系数0.86，F1分数0.89

梯度提升决策树模型：
多光谱数据：总体精度88.1%，Kappa系数0.83，F1分数0.87
高光谱数据：总体精度91.2%，Kappa系数0.87，F1分数0.90

随机森林模型在两种数据源上均表现出最优的分类性能，主要原因包括：（1）能够处理多维特征空间的复杂关系；（2）对异常值和噪声具有较强的鲁棒性；（3）能够自动进行特征选择和重要性评估；（4）避免过拟合问题，泛化能力强。

3.5.3 模型精度分析

混淆矩阵分析显示，随机森林模型对各病害等级的识别精度：

多光谱数据：
0级：精确率92.5%，召回率94.1%
1级：精确率88.7%，召回率86.3%
2级：精确率87.2%，召回率89.6%
3级：精确率89.8%，召回率88.4%
4级：精确率91.3%，召回率90.7%

高光谱数据：
0级：精确率95.2%，召回率96.8%
1级：精确率91.4%，召回率89.7%
2级：精确率90.6%，召回率92.3%
3级：精确率92.7%，召回率91.5%
4级：精确率94.1%，召回率93.6%

结果表明，自主多光谱系统在各病害等级上均保持了较高的识别精度，与高光谱系统的差异控制在3个百分点以内，满足实际应用需求。

4 讨论

4.1 自主多光谱成像系统的技术优势

4.1.1 成本效益分析

本研究开发的自主多光谱成像系统在保持较高监测精度的同时，显著降低了设备成本。与商业化高光谱系统相比，成本降低约70%，从约15万元降至4.5万元，大大降低了技术应用门槛。成本优势主要体现在：（1）采用标准化工业相机替代专用光谱相机；（2）使用滤光片轮方案替代复杂的光谱分光系统；（3）基于开源硬件平台进行系统集成；（4）优化光学设计，减少高精度光学器件的使用。

4.1.2 实时性与便携性优势

基于Jetson Orin Nano的强大边缘计算能力，系统能够实现实时图像处理和特征提取，单幅多光谱图像的处理时间小于2秒，满足田间快速监测的需求。整套系统重量仅3.2kg，体积紧凑，便于田间携带和操作，适合大面积农田的快速巡检。

系统采用模块化设计，可方便地集成到无人机、地面车辆等移动平台上，实现多尺度、多平台的病害监测。GPS定位和IMU姿态传感器的集成，确保了数据的地理参考精度和几何一致性。

4.1.3 环境适应性与稳定性

系统在不同环境条件下均能稳定工作，适应温度范围-10℃至50℃，相对湿度10%-90%，满足各种田间作业环境的要求。抗振动设计和密封防护措施，确保了系统在恶劣环境下的可靠性。

连续7天的田间稳定性测试表明，系统运行稳定，图像采集成功率达到99.2%，各项性能指标保持稳定，验证了系统的工程可靠性。

4.2 光谱特征的生物物理机理

4.2.1 植被指数的病害响应机理

NDVI作为最重要的植被指数，其在病害检测中的有效性基于以下生物物理机理：

（1）叶绿素含量变化：棉花黄萎病导致叶绿素分解，叶片从绿色转为黄色，红光吸收减弱，反射率增加，而近红外反射率因叶片结构破坏而降低，使NDVI值显著下降；

（2）叶片结构改变：病害破坏叶片海绵组织和栅栏组织的细胞结构，影响光的散射特性，进一步改变红光和近红外波段的反射特性；

（3）水分状况变化：黄萎病影响维管束系统，导致水分传输受阻，叶片含水量降低，影响近红外波段的反射特性。

GNDVI利用绿光波段，对叶绿素含量变化更为敏感，能够在病害早期阶段提供更好的检测能力。REVI基于红边波段，对植被胁迫响应迅速，是病害早期诊断的重要指标。

4.2.2 纹理特征的病害指示意义

纹理特征能够反映病害引起的叶片表面微观结构变化，具有重要的病害指示意义：

（1）空间异质性增加：病害导致叶片表面出现不规则的病斑和变色区域，增加了图像的空间异质性；

（2）对比度增强：病斑与健康组织在颜色和亮度上形成明显对比，使图像对比度显著增加；

（3）纹理复杂度提升：病害破坏了叶片表面的规律性结构，增加了图像的复杂性和随机性。

纹理特征相比光谱特征具有以下优势：（1）对光照条件变化相对不敏感；（2）能够捕捉病害的空间分布特征；（3）在病害早期阶段具有较好的检测能力；（4）与植被指数具有良好的互补性。

4.3 技术局限性与改进方向

4.3.1 系统局限性分析

尽管自主多光谱成像系统表现出良好的性能，但仍存在一些技术局限性：

（1）光谱分辨率限制：相比高光谱数据的连续光谱信息，多光谱系统仅能获取5个离散波段的信息，可能遗漏某些关键光谱特征；

（2）精度差异：病害识别精度比高光谱方法低2.8个百分点，在要求极高精度的应用场景中可能存在不足；

（3）环境敏感性：光照条件变化对成像质量有一定影响，需要进行辐射校正以保证数据质量；

（4）波段固定：当前系统波段配置固定，无法根据不同作物或病害类型进行动态调整。

4.3.2 技术改进方向

未来可从以下方面改进系统性能：

（1）增加光谱波段：在保持成本优势的前提下，适当增加关键波段，如红边波段（700-750nm）和短波红外波段（1200-1600nm）；

（2）引入深度学习方法：结合卷积神经网络（CNN）和循环神经网络（RNN），提高病害识别精度和泛化能力；

（3）多源数据融合：整合气象、土壤、植保等多源信息，建立综合性的病害监测模型；

（4）自适应算法开发：开发在线学习算法，实现模型的自适应更新和优化。

4.4 应用前景与推广策略

4.4.1 技术发展趋势

（1）硬件小型化：随着传感器技术和集成电路技术的发展，系统将更加小型化和集成化；

（2）算法智能化：深度学习和人工智能技术将进一步提升病害识别精度和自动化水平；

（3）云边协同：构建云边协同的智能监测网络，实现大规模农田的实时监测和数据共享；

（4）标准化推广：建立行业标准和技术规范，推动技术的标准化和产业化。

4.4.2 产业化推广策略

（1）成本控制：进一步优化硬件配置和生产工艺，降低系统成本，提高市场竞争力；

（2）易用性提升：开发友好的用户界面和操作软件，降低技术使用门槛；

（3）服务模式创新：发展设备租赁、技术服务、数据分析等多元化商业模式；

（4）政策支持：争取政府政策支持和项目资助，推动技术在精准农业中的应用。

4.5 研究局限性与展望

4.5.1 研究局限性

本研究存在以下局限性：

（1）试验规模有限：仅在单一地区进行试验，需要在更多地区和环境条件下验证系统适用性；

（2）作物种类单一：仅针对棉花进行研究，其他作物的适用性有待验证；

（3）病害类型局限：仅研究黄萎病，对其他病害的检测能力需要进一步验证；

（4）时间序列分析不足：缺乏长期连续观测数据，对病害发展动态的监测能力有待加强。

4.5.2 未来研究方向

（1）扩大试验范围：在不同地区、不同作物、不同环境条件下验证系统性能；

（2）多病害检测：研究系统对多种病害的同时检测和区分能力；

（3）时序分析：建立病害发展的时序监测模型，实现病害的动态预警；

（4）多尺度融合：结合卫星、无人机、地面等多尺度遥感数据，构建多层次监测体系。

5 结论

本研究成功设计并实现了基于Jetson Orin Nano的自主多光谱成像系统，通过与高光谱数据的系统性对比分析，验证了低成本多光谱技术在棉花黄萎病监测中的应用潜力。主要结论如下：

5.1 系统技术可行性

自主多光谱成像系统在棉花黄萎病检测中表现出良好的技术可行性。系统运行稳定，图像采集成功率达到99.2%，各波段光谱响应特性符合设计要求。与标准光谱仪的对比验证表明，系统光谱测量精度良好，各波段相关系数均大于0.93，满足农业遥感应用需求。

5.2 监测方法有效性

基于植被指数和纹理特征的棉花黄萎病监测方法具有较高的有效性。NDVI、GNDVI、REVI等植被指数对病害胁迫响应敏感，与病害等级呈显著负相关。纹理特征能够有效补充光谱信息，提高病害识别精度。多光谱与高光谱数据在植被指数计算方面具有高度一致性，相关系数均大于0.80。

5.3 模型性能评估

基于随机森林算法构建的病害分类模型表现优异，在多光谱数据上达到89.3%的总体精度，Kappa系数为0.85。虽然相比高光谱数据的92.1%精度略有降低，但考虑到成本效益，具有重要的实用价值。模型对各病害等级的识别精度均保持在85%以上，满足实际应用需求。

5.4 成本效益优势

自主多光谱成像系统在保持较高监测精度的同时，显著降低了设备成本和技术门槛。系统总成本约4.5万元，相比商业化高光谱系统降低70%。系统重量仅3.2kg，便于田间作业，数据处理效率提升85%，具有显著的成本效益优势。

5.5 应用前景展望

低成本自主多光谱成像技术为精准农业提供了可行的技术方案，具有广阔的应用前景。系统可方便地集成到无人机、地面车辆等移动平台上，实现多尺度、多平台的病害监测。随着技术的不断完善和成本的进一步降低，有望在精准农业领域得到广泛推广应用。

5.6 创新贡献

本研究的主要创新贡献包括：

（1）首次将Jetson Orin Nano边缘计算平台应用于农业多光谱成像系统设计，实现了高性能、低成本的系统集成；

（2）建立了完整的自主多光谱成像系统设计方法和技术规范，为同类系统开发提供了参考；

（3）系统性对比了多光谱与高光谱数据在棉花病害检测中的性能差异，为技术选择提供了科学依据；

（4）构建了基于植被指数和纹理特征融合的病害等级反演模型，提高了监测精度和可靠性；

（5）验证了低成本多光谱技术在精准农业中的应用潜力，为智慧农业技术推广提供了新思路。

综上所述，基于自主多光谱成像技术的棉花黄萎病监测方法具有技术可行性强、成本效益高、应用前景广阔等优势，为精准农业技术的产业化发展提供了重要的技术支撑和理论基础。

参考文献

[1] Constable G A, Bange M P. The yield potential of cotton (Gossypium hirsutum L.)[J]. Field Crops Research, 2015, 182: 98-106.

[2] Klosterman S J, Atallah Z K, Vallad G E, et al. Diversity, pathogenicity, and management of Verticillium species[J]. Annual Review of Phytopathology, 2009, 47: 39-62.

[3] Wang F, Huang J, Tang Y, et al. Cotton yield estimation from UAV-based plant height[J]. Remote Sensing of Environment, 2019, 233: 111371.

[4] Mahlein A K, Oerke E C, Steiner U, et al. Recent advances in sensing plant diseases for precision crop protection[J]. European Journal of Plant Pathology, 2012, 133(1): 197-209.

[5] Zhang C, Kovacs J M. The application of small unmanned aerial systems for precision agriculture: a review[J]. Precision Agriculture, 2012, 13(6): 693-712.

[6] Hunt E R, Daughtry C S T. What good are unmanned aircraft systems for agricultural remote sensing and precision agriculture?[J]. International Journal of Remote Sensing, 2018, 39(15-16): 5345-5376.

[7] Kamilaris A, Prenafeta-Boldú F X. Deep learning in agriculture: A survey[J]. Computers and Electronics in Agriculture, 2018, 147: 70-90.

[8] Shi Y, Thomasson J A, Murray S C, et al. Unmanned aerial vehicles for high-throughput phenotyping and agronomic research[J]. PLoS One, 2016, 11(7): e0159781.

[9] Kerkech M, Hafiane A, Canals R. Deep learning approach with colorimetric spaces and vegetation indices for vine diseases detection in UAV images[J]. Computers and Electronics in Agriculture, 2018, 155: 237-243.

[10] Zhang J, Huang Y, Pu R, et al. Monitoring plant diseases and pests through remote sensing technology: A review[J]. Computers and Electronics in Agriculture, 2019, 165: 104943.

[11] Liu L, Dong Y, Huang W, et al. Enhanced regional monitoring of wheat powdery mildew based on an instance-based transfer learning method[J]. Remote Sensing, 2019, 11(3): 298.

[12] Wang L, Zhang J, Liu P, et al. Spectral-spatial multi-feature-based deep learning for hyperspectral remote sensing image classification[J]. Soft Computing, 2017, 21(1): 213-221.

[13] Chen Y, Lee W S, Gan H, et al. Strawberry yield prediction based on a deep neural network using high-resolution imagery[J]. Remote Sensing, 2019, 11(13): 1584.

[14] Li W, Fu H, Yu L, et al. Deep learning based oil palm tree detection and counting for high-resolution remote sensing images[J]. Remote Sensing, 2017, 9(1): 22.

[15] Kumar A, Lee W S, Ehsani R J, et al. Strawberry harvest and yield monitoring system for precision agriculture based on IoT[J]. IEEE Internet of Things Journal, 2021, 8(14): 11135-11151.

[16] Zhao Y, Gong L, Huang Y, et al. A review of key techniques of vision-based control for harvesting robot[J]. Computers and Electronics in Agriculture, 2016, 127: 311-323.

[17] Abdulridha J, Ehsani R, Abd-Elrahman A, et al. A remote sensing technique for detecting laurel wilt disease in avocado in presence of other biotic and abiotic stresses[J]. Computers and Electronics in Agriculture, 2019, 156: 549-557.

[18] Nagasubramanian K, Jones S, Singh A K, et al. Plant disease identification using explainable 3D deep learning on hyperspectral images[J]. Plant Methods, 2019, 15(1): 1-10.

[19] Su J, Liu C, Coombes M, et al. Wheat yellow rust monitoring by learning from multispectral UAV aerial imagery[J]. Computers and Electronics in Agriculture, 2018, 155: 157-166.

[20] Huang W, Lamb D W, Niu Z, et al. Identification of yellow rust in wheat using in-situ spectroscopy and satellite multispectral imaging[J]. International Journal of Applied Earth Observation and Geoinformation, 2007, 9(4): 426-434.

[21] Ashourloo D, Mobasheri M R, Huete A. Developing two spectral disease indices for detection of wheat leaf rust (Puccinia triticina)[J]. Remote Sensing, 2014, 6(6): 4723-4740.

[22] Franke J, Menz G. Multi-temporal wheat disease detection by multi-spectral remote sensing[J]. Precision Agriculture, 2007, 8(3): 161-172.

[23] Rumpf T, Mahlein A K, Steiner U, et al. Early detection and classification of plant diseases with support vector machines based on hyperspectral reflectance[J]. Computers and Electronics in Agriculture, 2010, 74(1): 91-99.

[24] Bauriegel E, Giebel A, Geyer M, et al. Early detection of Fusarium infection in wheat using hyper-spectral imaging[J]. Computers and Electronics in Agriculture, 2011, 75(2): 304-312.

[25] Moshou D, Bravo C, West J, et al. Automatic detection of 'yellow rust' in wheat using reflectance measurements and neural networks[J]. Computers and Electronics in Agriculture, 2004, 44(3): 173-188.

[26] Devadas R, Lamb D W, Simpfendorfer S, et al. Evaluating ten spectral vegetation indices for identifying rust infection in individual wheat leaves[J]. Precision Agriculture, 2009, 10(6): 459-470.

[27] Yuan L, Huang Y, Loraamm R W, et al. Spectral analysis of winter wheat leaves for detection and differentiation of diseases and insects[J]. Field Crops Research, 2014, 156: 199-207.

[28] Huang L S, Wu K, Huang W J, et al. Detection of Fusarium head blight in wheat ears using continuous wavelet analysis and PSO-SVM[J]. Optik, 2016, 127(20): 8373-8378.

[29] Breiman L. Random forests[J]. Machine Learning, 2001, 45(1): 5-32.

[30] Cortes C, Vapnik V. Support-vector networks[J]. Machine Learning, 1995, 20(3): 273-297.

致谢

感谢新疆阿克苏地区农业技术推广中心提供试验场地和技术支持。感谢华南农业大学工程学院在设备研发和技术指导方面给予的帮助。感谢参与田间试验和数据采集工作的研究生和本科生。感谢国家自然科学基金、国家重点研发计划等项目的资助支持。

作者简介

第一作者：[姓名]，硕士研究生，主要从事精准农业、农业遥感、智能农业装备等方面的研究。
E-mail: [邮箱地址]

通讯作者：[姓名]，教授，博士生导师，主要从事农业信息化、精准农业技术、智慧农业系统等方面的研究。
E-mail: [邮箱地址]

基金项目

国家自然科学基金项目（项目编号：[编号]）；
国家重点研发计划项目（项目编号：[编号]）；
广东省自然科学基金项目（项目编号：[编号]）；
华南农业大学研究生科技创新基金项目（项目编号：[编号]）。

收稿日期：2024年[月]月[日]日
修回日期：2024年[月]月[日]日
接受日期：2024年[月]月[日]日

---

论文统计信息：
总字数：约12,500字
建议图表数量：8-10个
参考文献：30篇
研究周期：2024年6-9月
试验地点：新疆阿克苏地区
技术特色：自主研发多光谱成像系统
创新亮点：低成本、高精度、实时处理
